// Debug configuration utility for rendi.dev integration

/**
 * Check all environment variables and configuration
 */
export function debugConfiguration() {
  console.log('=== Rendi.dev Configuration Debug ===');
  
  // Check environment variables
  const config = {
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
    supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing',
    supabaseBucket: process.env.NEXT_PUBLIC_SUPABASE_BUCKET,
    rendiApiKey: process.env.RENDI_API_KEY ? '✅ Set (server-side)' : '❌ Missing (server-side)',
  };
  
  console.log('Environment Variables:');
  console.log('- NEXT_PUBLIC_SUPABASE_URL:', config.supabaseUrl);
  console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY:', config.supabaseKey);
  console.log('- NEXT_PUBLIC_SUPABASE_BUCKET:', config.supabaseBucket);
  console.log('- RENDI_API_KEY:', config.rendiApiKey);
  
  // Check for common issues
  const issues = [];
  
  if (!config.supabaseUrl) {
    issues.push('Missing NEXT_PUBLIC_SUPABASE_URL');
  } else if (!config.supabaseUrl.includes('supabase.co')) {
    issues.push('NEXT_PUBLIC_SUPABASE_URL format looks incorrect');
  }
  
  if (config.supabaseKey === '❌ Missing') {
    issues.push('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY');
  }
  
  if (!config.supabaseBucket) {
    issues.push('Missing NEXT_PUBLIC_SUPABASE_BUCKET');
  } else if (config.supabaseBucket !== 'temp-video-files') {
    issues.push(`Bucket name '${config.supabaseBucket}' should be 'temp-video-files'`);
  }
  
  if (config.rendiApiKey === '❌ Missing (server-side)') {
    issues.push('Missing RENDI_API_KEY (server-side only)');
  }
  
  if (issues.length > 0) {
    console.log('\n❌ Configuration Issues Found:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  } else {
    console.log('\n✅ Configuration looks good!');
  }
  
  console.log('\n=== Next Steps ===');
  console.log('1. Fix any configuration issues above');
  console.log('2. Ensure Supabase bucket has public policies');
  console.log('3. Test with the "Test" button in the UI');
  console.log('4. Check browser console for detailed error messages');
  
  console.log('=====================================');
  
  return {
    config,
    issues,
    isValid: issues.length === 0
  };
}

/**
 * Test Supabase connection with detailed logging
 */
export async function debugSupabaseConnection() {
  console.log('=== Supabase Connection Debug ===');
  
  try {
    const { createClient } = await import('@supabase/supabase-js');
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
    const bucketName = process.env.NEXT_PUBLIC_SUPABASE_BUCKET!;
    
    console.log('Creating Supabase client...');
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    console.log('Testing bucket list...');
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Failed to list buckets:', listError);
      return false;
    }
    
    console.log('✅ Buckets found:', buckets?.map(b => b.name));
    
    const targetBucket = buckets?.find(b => b.name === bucketName);
    if (!targetBucket) {
      console.error(`❌ Target bucket '${bucketName}' not found`);
      return false;
    }
    
    console.log('✅ Target bucket found:', targetBucket);
    
    console.log('Testing file list in bucket...');
    const { data: files, error: filesError } = await supabase.storage
      .from(bucketName)
      .list('', { limit: 5 });
    
    if (filesError) {
      console.error('❌ Failed to list files:', filesError);
      return false;
    }
    
    console.log('✅ File list successful. Files found:', files?.length || 0);
    
    console.log('Testing upload...');
    const testFileName = `debug_test_${Date.now()}.txt`;
    const testContent = new Blob(['debug test'], { type: 'text/plain' });
    
    const { error: uploadError } = await supabase.storage
      .from(bucketName)
      .upload(testFileName, testContent);
    
    if (uploadError) {
      console.error('❌ Upload test failed:', uploadError);
      return false;
    }
    
    console.log('✅ Upload test successful');
    
    console.log('Testing delete...');
    const { error: deleteError } = await supabase.storage
      .from(bucketName)
      .remove([testFileName]);
    
    if (deleteError) {
      console.warn('⚠️ Delete test failed (but upload worked):', deleteError);
    } else {
      console.log('✅ Delete test successful');
    }
    
    console.log('✅ All Supabase tests passed!');
    return true;
    
  } catch (error) {
    console.error('❌ Supabase debug failed:', error);
    return false;
  }
}

/**
 * Run all debug checks
 */
export async function runFullDebug() {
  console.log('🔍 Running full debug check...\n');
  
  const configResult = debugConfiguration();
  
  if (!configResult.isValid) {
    console.log('\n❌ Configuration issues found. Fix these first before testing connections.');
    return false;
  }
  
  console.log('\n🔗 Testing Supabase connection...');
  const supabaseResult = await debugSupabaseConnection();
  
  if (supabaseResult) {
    console.log('\n🎉 All debug checks passed! The rendi.dev integration should work.');
  } else {
    console.log('\n❌ Some debug checks failed. Check the logs above for details.');
  }
  
  return supabaseResult;
}
