import P5 from 'p5';
import { AudioData, WaveformStyle, VideoSettings, VisualizationParams } from '../../types';
import { FrameData, FrameGenerationProgress } from './types';
import { convertToWebP } from './supabase-utils';

/**
 * Create a P5 instance for frame generation (similar to existing VideoGenerator)
 */
async function createP5Instance(width: number, height: number): Promise<P5> {
  return new Promise((resolve) => {
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    document.body.appendChild(tempContainer);

    const sketch = (p: P5) => {
      p.setup = () => {
        p.createCanvas(width, height);
        p.colorMode(p.HSB, 360, 100, 100, 100);
        p.noLoop();
        
        const originalRemove = p.remove;
        p.remove = () => {
          originalRemove.call(p);
          if (tempContainer.parentNode) {
            document.body.removeChild(tempContainer);
          }
        };
        resolve(p);
      };
    };

    new P5(sketch, tempContainer);
  });
}

/**
 * Draw P5 waveform (reused from existing VideoGenerator)
 */
function drawP5Waveform(
  p: P5, 
  audioDataArray: Float32Array, 
  style: WaveformStyle, 
  time: number, 
  width: number, 
  height: number
) {
  p.background(0, 0, 0);
  
  if (style === 'circular_spectrum') {
    const centerX = width / 2;
    const centerY = height / 2;
    const scaleFactor = Math.min(width, height) / 800;
    const maxRadius = Math.min(centerX, centerY) * 0.8;
    const centerSize = Math.floor(20 * scaleFactor);
    
    // Use only first 96 frequency bins to avoid empty high frequencies
    const activeBins = 96;
    const usedData = audioDataArray.slice(0, activeBins);
    
    // Draw frequency bars in circle
    for (let i = 0; i < 128; i++) {
      const dataIndex = Math.floor((i / 128) * activeBins);
      const amplitude = usedData[dataIndex] || 0;
      
      const angle = (i / 128) * Math.PI * 2 - Math.PI / 2;
      const barLength = (amplitude / 255) * maxRadius * 0.8;
      
      const innerRadius = centerSize + Math.floor(10 * scaleFactor);
      const outerRadius = innerRadius + barLength;
      
      const x1 = centerX + Math.cos(angle) * innerRadius;
      const y1 = centerY + Math.sin(angle) * innerRadius;
      const x2 = centerX + Math.cos(angle) * outerRadius;
      const y2 = centerY + Math.sin(angle) * outerRadius;
      
      // Color based on frequency and amplitude
      const hue = (i / 128) * 360;
      const saturation = 70 + (amplitude / 255) * 30;
      const brightness = 60 + (amplitude / 255) * 40;
      const alpha = 80 + (amplitude / 255) * 20;
      
      p.stroke(hue, saturation, brightness, alpha);
      p.strokeWeight(Math.max(1, Math.floor(2 * scaleFactor)));
      p.line(x1, y1, x2, y2);
    }
    
    // Center circle
    p.fill(280, 60, 80, 90);
    p.noStroke();
    p.circle(centerX, centerY, centerSize);
    
    // Pulsing ring
    const pulseAmplitude = Math.floor(10 * scaleFactor);
    const pulseRadius = centerSize + p.sin(time * 4) * pulseAmplitude;
    p.noFill();
    p.stroke(280, 80, 90, 60);
    p.strokeWeight(Math.max(1, Math.floor(2 * scaleFactor)));
    p.circle(centerX, centerY, pulseRadius);
  }
}

/**
 * Generate all frames for the video
 */
export async function generateFrames(
  audioData: AudioData,
  style: WaveformStyle,
  settings: VideoSettings,
  visualizationParams: VisualizationParams,
  onProgress?: (progress: FrameGenerationProgress) => void
): Promise<FrameData[]> {
  const [width, height] = settings.resolution.split('x').map(Number);
  const totalFrames = Math.floor(audioData.metadata.duration * settings.fps);
  const frames: FrameData[] = [];
  
  if (onProgress) {
    onProgress({
      currentFrame: 0,
      totalFrames,
      percentage: 0,
      message: 'Initializing frame generation...'
    });
  }
  
  // Create P5 instance
  const p5Instance = await createP5Instance(width, height);
  
  try {
    for (let frame = 0; frame < totalFrames; frame++) {
      const currentTime = frame / settings.fps;
      const dataIndex = Math.floor((currentTime / audioData.metadata.duration) * audioData.frequencyData.length);
      const audioDataArray = audioData.frequencyData[dataIndex] || new Float32Array(128);
      
      // Apply visualization parameters
      const scaledData = new Float32Array(audioDataArray.length);
      for (let i = 0; i < audioDataArray.length; i++) {
        scaledData[i] = audioDataArray[i] * visualizationParams.sensitivity;
      }
      
      const animationTime = currentTime * 2.5;
      
      // Draw frame
      drawP5Waveform(p5Instance, scaledData, style, animationTime, width, height);
      
      // Get frame data
      const canvas = p5Instance.drawingContext.canvas as HTMLCanvasElement;
      const dataUrl = canvas.toDataURL('image/png');
      
      // Convert to WebP for better compression
      const webpBlob = await convertToWebP(dataUrl, 0.8);
      
      frames.push({
        index: frame,
        dataUrl,
        webpBlob,
        timestamp: currentTime
      });
      
      // Report progress
      if (onProgress) {
        const percentage = Math.round(((frame + 1) / totalFrames) * 100);
        onProgress({
          currentFrame: frame + 1,
          totalFrames,
          percentage,
          message: `Generated frame ${frame + 1} of ${totalFrames}`
        });
      }
      
      // Small delay every 10 frames to prevent blocking
      if (frame % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }
    
    return frames;
    
  } finally {
    // Clean up P5 instance
    if (p5Instance) {
      try {
        p5Instance.remove();
      } catch (error) {
        console.warn('Error removing P5 instance:', error);
      }
    }
  }
}

/**
 * Generate frames in batches to manage memory usage
 */
export async function generateFramesInBatches(
  audioData: AudioData,
  style: WaveformStyle,
  settings: VideoSettings,
  visualizationParams: VisualizationParams,
  batchSize: number = 50,
  onProgress?: (progress: FrameGenerationProgress) => void,
  onBatchComplete?: (batch: FrameData[]) => Promise<void>
): Promise<void> {
  const [width, height] = settings.resolution.split('x').map(Number);
  const totalFrames = Math.floor(audioData.metadata.duration * settings.fps);
  
  if (onProgress) {
    onProgress({
      currentFrame: 0,
      totalFrames,
      percentage: 0,
      message: 'Starting batch frame generation...'
    });
  }
  
  // Create P5 instance
  const p5Instance = await createP5Instance(width, height);
  
  try {
    for (let batchStart = 0; batchStart < totalFrames; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize, totalFrames);
      const batch: FrameData[] = [];
      
      for (let frame = batchStart; frame < batchEnd; frame++) {
        const currentTime = frame / settings.fps;
        const dataIndex = Math.floor((currentTime / audioData.metadata.duration) * audioData.frequencyData.length);
        const audioDataArray = audioData.frequencyData[dataIndex] || new Float32Array(128);
        
        // Apply visualization parameters
        const scaledData = new Float32Array(audioDataArray.length);
        for (let i = 0; i < audioDataArray.length; i++) {
          scaledData[i] = audioDataArray[i] * visualizationParams.sensitivity;
        }
        
        const animationTime = currentTime * 2.5;
        
        // Draw frame
        drawP5Waveform(p5Instance, scaledData, style, animationTime, width, height);
        
        // Get frame data
        const canvas = p5Instance.drawingContext.canvas as HTMLCanvasElement;
        const dataUrl = canvas.toDataURL('image/png');
        
        // Convert to WebP
        const webpBlob = await convertToWebP(dataUrl, 0.8);
        
        batch.push({
          index: frame,
          dataUrl,
          webpBlob,
          timestamp: currentTime
        });
        
        // Report progress
        if (onProgress) {
          const percentage = Math.round(((frame + 1) / totalFrames) * 100);
          onProgress({
            currentFrame: frame + 1,
            totalFrames,
            percentage,
            message: `Generated frame ${frame + 1} of ${totalFrames} (batch ${Math.floor(batchStart / batchSize) + 1})`
          });
        }
      }
      
      // Process batch (e.g., upload to storage)
      if (onBatchComplete) {
        await onBatchComplete(batch);
      }
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
  } finally {
    // Clean up P5 instance
    if (p5Instance) {
      try {
        p5Instance.remove();
      } catch (error) {
        console.warn('Error removing P5 instance:', error);
      }
    }
  }
}
