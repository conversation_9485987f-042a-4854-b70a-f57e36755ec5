// Rendi.dev video generation specific types

export interface RendiVideoGenerationOptions {
  audioData: import('../../types').AudioData;
  style: import('../../types').WaveformStyle;
  settings: import('../../types').VideoSettings;
  visualizationParams: import('../../types').VisualizationParams;
}

export interface FrameGenerationProgress {
  currentFrame: number;
  totalFrames: number;
  percentage: number;
  message: string;
}

export interface SupabaseUploadProgress {
  uploadedFrames: number;
  totalFrames: number;
  percentage: number;
  message: string;
}

export interface RendiProcessingProgress {
  status: 'pending' | 'running' | 'success' | 'failed';
  percentage: number;
  message: string;
  processingTime?: number;
}

export interface RendiVideoGenerationResult {
  success: boolean;
  videoUrl?: string;
  error?: string;
  processingTime?: number;
  fileSize?: number;
}

export interface FrameData {
  index: number;
  dataUrl: string;
  webpBlob: Blob;
  timestamp: number;
}

export interface SupabaseFrameUpload {
  frameIndex: number;
  fileName: string;
  publicUrl: string;
  uploadedAt: Date;
}
