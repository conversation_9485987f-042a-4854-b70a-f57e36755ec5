import { createClient } from '@supabase/supabase-js';
import { FrameData, SupabaseFrameUpload, SupabaseUploadProgress } from './types';

// Create Supabase client dynamically
function getSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
  return createClient(supabaseUrl, supabaseKey);
}

function getBucketName() {
  return process.env.NEXT_PUBLIC_SUPABASE_BUCKET!;
}

/**
 * Convert canvas data URL to WebP blob for better compression
 */
export async function convertToWebP(dataUrl: string, quality: number = 0.8): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx?.drawImage(img, 0, 0);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert to WebP'));
          }
        },
        'image/webp',
        quality
      );
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = dataUrl;
  });
}

/**
 * Generate a unique session ID for organizing frames
 */
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Upload a single frame to Supabase storage
 */
export async function uploadFrame(
  frameData: FrameData,
  sessionId: string
): Promise<SupabaseFrameUpload> {
  const supabase = getSupabaseClient();
  const bucketName = getBucketName();
  const fileName = `${sessionId}/frame_${frameData.index.toString().padStart(6, '0')}.webp`;

  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(fileName, frameData.webpBlob, {
        contentType: 'image/webp',
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      throw new Error(`Failed to upload frame ${frameData.index}: ${error.message}`);
    }

    // Get public URL
    const { data: publicUrlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(fileName);

    return {
      frameIndex: frameData.index,
      fileName,
      publicUrl: publicUrlData.publicUrl,
      uploadedAt: new Date()
    };
  } catch (error) {
    throw new Error(`Upload failed for frame ${frameData.index}: ${error}`);
  }
}

/**
 * Upload frames in chunks with progress tracking
 */
export async function uploadFramesInChunks(
  frames: FrameData[],
  sessionId: string,
  chunkSize: number = 5,
  onProgress?: (progress: SupabaseUploadProgress) => void
): Promise<SupabaseFrameUpload[]> {
  const uploads: SupabaseFrameUpload[] = [];
  const totalFrames = frames.length;
  
  for (let i = 0; i < frames.length; i += chunkSize) {
    const chunk = frames.slice(i, i + chunkSize);
    
    // Upload chunk in parallel
    const chunkPromises = chunk.map(frame => uploadFrame(frame, sessionId));
    
    try {
      const chunkResults = await Promise.all(chunkPromises);
      uploads.push(...chunkResults);
      
      // Report progress
      if (onProgress) {
        const uploadedFrames = uploads.length;
        const percentage = Math.round((uploadedFrames / totalFrames) * 100);
        onProgress({
          uploadedFrames,
          totalFrames,
          percentage,
          message: `Uploaded ${uploadedFrames} of ${totalFrames} frames`
        });
      }
      
      // Small delay between chunks to avoid overwhelming the API
      if (i + chunkSize < frames.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } catch (error) {
      // If a chunk fails, try to clean up uploaded frames
      await cleanupFrames(sessionId);
      throw new Error(`Failed to upload chunk starting at frame ${i}: ${error}`);
    }
  }
  
  return uploads;
}

/**
 * Clean up uploaded frames from Supabase storage
 */
export async function cleanupFrames(sessionId: string): Promise<void> {
  const supabase = getSupabaseClient();
  const bucketName = getBucketName();

  try {
    // List all files in the session folder
    const { data: files, error: listError } = await supabase.storage
      .from(bucketName)
      .list(sessionId);

    if (listError) {
      console.warn(`Failed to list files for cleanup: ${listError.message}`);
      return;
    }

    if (!files || files.length === 0) {
      return;
    }

    // Delete all files in the session folder
    const filePaths = files.map(file => `${sessionId}/${file.name}`);
    const { error: deleteError } = await supabase.storage
      .from(bucketName)
      .remove(filePaths);

    if (deleteError) {
      console.warn(`Failed to delete some files during cleanup: ${deleteError.message}`);
    }
  } catch (error) {
    console.warn(`Cleanup failed for session ${sessionId}:`, error);
  }
}

/**
 * Get the public URL for the first frame (for thumbnail purposes)
 */
export function getFrameUrl(sessionId: string, frameIndex: number): string {
  const supabase = getSupabaseClient();
  const bucketName = getBucketName();
  const fileName = `${sessionId}/frame_${frameIndex.toString().padStart(6, '0')}.webp`;
  const { data } = supabase.storage
    .from(bucketName)
    .getPublicUrl(fileName);

  return data.publicUrl;
}

/**
 * Check if Supabase storage is available and accessible
 */
export async function checkStorageHealth(): Promise<boolean> {
  const supabase = getSupabaseClient();
  const bucketName = getBucketName();

  try {
    // Test 1: Can we list buckets?
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    if (listError) {
      console.warn('Failed to list buckets:', listError.message);
      return false;
    }

    // Test 2: Does our bucket exist?
    const bucketExists = buckets && buckets.some(bucket => bucket.name === bucketName);
    if (!bucketExists) {
      console.warn(`Bucket '${bucketName}' not found in:`, buckets?.map(b => b.name));
      return false;
    }

    // Test 3: Can we list files in our bucket? (This tests read permissions)
    const { error: listFilesError } = await supabase.storage
      .from(bucketName)
      .list('', { limit: 1 });

    if (listFilesError) {
      console.warn('Failed to list files in bucket:', listFilesError.message);
      return false;
    }

    return true;
  } catch (error) {
    console.warn('Storage health check failed:', error);
    return false;
  }
}
