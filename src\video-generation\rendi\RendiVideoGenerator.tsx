'use client';

import React, { useState, useCallback } from 'react';
import { AudioData, WaveformStyle, VideoSettings, VisualizationParams } from '../../types';
import { 
  RendiVideoGenerationOptions, 
  RendiVideoGenerationResult,
  FrameGenerationProgress,
  SupabaseUploadProgress,
  RendiProcessingProgress,
  FrameData
} from './types';
import { generateFramesInBatches } from './frame-generator';
import { 
  uploadFramesInChunks, 
  generateSessionId, 
  cleanupFrames,
  checkStorageHealth 
} from './supabase-utils';
import { RendiApiClient } from './api-client';
import { runAllTests, logTestResults } from './test-utils';
import { runFullDebug } from './debug-config';

interface RendiVideoGeneratorProps {
  audioData: AudioData;
  style: WaveformStyle;
  settings: VideoSettings;
  visualizationParams: VisualizationParams;
  onGenerationStart: () => void;
  onGenerationComplete: () => void;
}

type GenerationStage = 'idle' | 'generating_frames' | 'uploading_frames' | 'processing_video' | 'complete' | 'error';

export default function RendiVideoGenerator({
  audioData,
  style,
  settings,
  visualizationParams,
  onGenerationStart,
  onGenerationComplete
}: RendiVideoGeneratorProps) {
  const [stage, setStage] = useState<GenerationStage>('idle');
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('');
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');

  // Progress tracking for different stages
  const [frameProgress, setFrameProgress] = useState<FrameGenerationProgress>({
    currentFrame: 0,
    totalFrames: 0,
    percentage: 0,
    message: ''
  });

  const [uploadProgress, setUploadProgress] = useState<SupabaseUploadProgress>({
    uploadedFrames: 0,
    totalFrames: 0,
    percentage: 0,
    message: ''
  });

  const [processingProgress, setProcessingProgress] = useState<RendiProcessingProgress>({
    status: 'pending',
    percentage: 0,
    message: ''
  });

  const updateOverallProgress = useCallback(() => {
    let overallProgress = 0;
    let overallMessage = '';

    switch (stage) {
      case 'generating_frames':
        overallProgress = frameProgress.percentage * 0.4; // 40% of total
        overallMessage = frameProgress.message;
        break;
      case 'uploading_frames':
        overallProgress = 40 + (uploadProgress.percentage * 0.3); // 30% of total
        overallMessage = uploadProgress.message;
        break;
      case 'processing_video':
        overallProgress = 70 + (processingProgress.percentage * 0.3); // 30% of total
        overallMessage = processingProgress.message;
        break;
      case 'complete':
        overallProgress = 100;
        overallMessage = 'Video generation completed successfully!';
        break;
      case 'error':
        overallProgress = 0;
        overallMessage = message;
        break;
      default:
        overallProgress = 0;
        overallMessage = 'Ready to generate video';
    }

    setProgress(Math.round(overallProgress));
    setMessage(overallMessage);
  }, [stage, frameProgress, uploadProgress, processingProgress, message]);

  // Update overall progress when individual progresses change
  React.useEffect(() => {
    updateOverallProgress();
  }, [updateOverallProgress]);

  const generateVideo = async () => {
    if (isGenerating) return;

    // Validation checks
    if (audioData.metadata.duration > 900) {
      setStage('error');
      setMessage('Audio too long. Maximum duration is 15 minutes for server-side generation.');
      return;
    }

    if (audioData.metadata.duration < 1) {
      setStage('error');
      setMessage('Audio too short. Minimum duration is 1 second.');
      return;
    }

    setIsGenerating(true);
    onGenerationStart();

    const newSessionId = generateSessionId();
    setSessionId(newSessionId);

    // Reset all progress states
    setFrameProgress({ currentFrame: 0, totalFrames: 0, percentage: 0, message: '' });
    setUploadProgress({ uploadedFrames: 0, totalFrames: 0, percentage: 0, message: '' });
    setProcessingProgress({ status: 'pending', percentage: 0, message: '' });

    try {
      // Check storage health
      setStage('generating_frames');
      setMessage('Checking storage availability...');

      const storageHealthy = await checkStorageHealth();
      if (!storageHealthy) {
        throw new Error('Storage service is not available. Please try again later.');
      }

      // Stage 1: Generate frames in batches
      setStage('generating_frames');
      const frameUrls: string[] = [];
      let totalFramesGenerated = 0;

      await generateFramesInBatches(
        audioData,
        style,
        settings,
        visualizationParams,
        25, // Batch size
        (progress) => {
          setFrameProgress(progress);
          totalFramesGenerated = progress.currentFrame;
        },
        async (batch: FrameData[]) => {
          // Stage 2: Upload batch to Supabase
          setStage('uploading_frames');

          try {
            const uploads = await uploadFramesInChunks(
              batch,
              newSessionId,
              5, // Upload chunk size
              setUploadProgress
            );

            // Collect frame URLs
            uploads.forEach(upload => {
              frameUrls[upload.frameIndex] = upload.publicUrl;
            });
          } catch (uploadError) {
            console.error('Batch upload failed:', uploadError);
            throw new Error(`Failed to upload frames: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
          }
        }
      );

      // Verify all frames were uploaded
      const expectedFrames = Math.floor(audioData.metadata.duration * settings.fps);
      const actualFrames = frameUrls.filter(url => url).length;

      if (actualFrames < expectedFrames * 0.9) { // Allow 10% tolerance
        throw new Error(`Frame upload incomplete: ${actualFrames}/${expectedFrames} frames uploaded`);
      }

      // Stage 3: Submit to rendi.dev
      setStage('processing_video');
      setProcessingProgress({
        status: 'pending',
        percentage: 0,
        message: 'Submitting video generation request...'
      });

      try {
        const apiClient = new RendiApiClient();
        const resultUrl = await apiClient.generateVideoFromFrames(
          frameUrls.filter(url => url), // Remove any undefined URLs
          null, // No audio for now (could be added later)
          settings.fps,
          settings.resolution,
          (progress) => {
            setProcessingProgress(progress);

            // Add timeout check
            if (progress.processingTime && progress.processingTime > 300) { // 5 minutes
              throw new Error('Video processing is taking too long. Please try with a shorter video.');
            }
          }
        );

        if (!resultUrl) {
          throw new Error('No video URL returned from server');
        }

        // Success!
        setVideoUrl(resultUrl);
        setStage('complete');
        setMessage('Video generated successfully!');

        // Clean up frames after successful generation
        setTimeout(async () => {
          try {
            await cleanupFrames(newSessionId);
            console.log('Temporary frames cleaned up successfully');
          } catch (cleanupError) {
            console.warn('Failed to cleanup frames after success:', cleanupError);
          }
        }, 5000); // Wait 5 seconds before cleanup

      } catch (rendiError) {
        console.error('Rendi processing error:', rendiError);
        throw new Error(`Video processing failed: ${rendiError instanceof Error ? rendiError.message : 'Unknown server error'}`);
      }

    } catch (error) {
      console.error('Rendi video generation error:', error);
      setStage('error');

      // Provide more specific error messages
      let errorMessage = 'Unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;

        // Provide user-friendly messages for common errors
        if (errorMessage.includes('Storage service is not available')) {
          errorMessage = 'Storage service is temporarily unavailable. Please try again in a few minutes.';
        } else if (errorMessage.includes('Failed to upload frames')) {
          errorMessage = 'Failed to upload video frames. Please check your internet connection and try again.';
        } else if (errorMessage.includes('Video processing failed')) {
          errorMessage = 'Server-side video processing failed. Please try again or use the browser-based method.';
        } else if (errorMessage.includes('taking too long')) {
          errorMessage = 'Video processing is taking too long. Try with a shorter audio file or use the browser-based method.';
        }
      }

      setMessage(`Generation failed: ${errorMessage}`);

      // Cleanup on error
      if (newSessionId) {
        try {
          await cleanupFrames(newSessionId);
          console.log('Cleaned up frames after error');
        } catch (cleanupError) {
          console.warn('Failed to cleanup frames after error:', cleanupError);
        }
      }
    } finally {
      setIsGenerating(false);
      onGenerationComplete();
    }
  };

  const handleCleanup = async () => {
    if (!sessionId) {
      setMessage('No session to clean up');
      return;
    }

    try {
      setMessage('Cleaning up temporary files...');
      await cleanupFrames(sessionId);
      setMessage('Temporary files cleaned up successfully');
      setSessionId(''); // Clear session ID after cleanup
    } catch (error) {
      console.warn('Cleanup failed:', error);
      setMessage(`Cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleRetry = () => {
    // Reset state for retry
    setStage('idle');
    setProgress(0);
    setMessage('');
    setVideoUrl('');
    setFrameProgress({ currentFrame: 0, totalFrames: 0, percentage: 0, message: '' });
    setUploadProgress({ uploadedFrames: 0, totalFrames: 0, percentage: 0, message: '' });
    setProcessingProgress({ status: 'pending', percentage: 0, message: '' });

    // Clear session ID to start fresh
    setSessionId('');
  };

  const handleRunTests = async () => {
    setMessage('Running integration tests...');
    try {
      const results = await runAllTests();
      logTestResults(results);
      setMessage(`Tests completed: ${results.overall.success ? 'All passed' : 'Some failed'} - Check console for details`);
    } catch (error) {
      console.error('Test execution failed:', error);
      setMessage(`Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleFullDebug = async () => {
    setMessage('Running full debug check...');
    try {
      const success = await runFullDebug();
      setMessage(`Debug completed: ${success ? 'All checks passed' : 'Issues found'} - Check console for details`);
    } catch (error) {
      console.error('Debug execution failed:', error);
      setMessage(`Debug failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const getStageColor = () => {
    switch (stage) {
      case 'complete': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'generating_frames':
      case 'uploading_frames':
      case 'processing_video': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  const getProgressBarColor = () => {
    switch (stage) {
      case 'complete': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-blue-500';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">
          Server-side Generation (Rendi.dev)
        </h3>
        <div className="text-sm text-gray-400">
          High-quality • Server processing • No browser limits
        </div>
      </div>

      {/* Progress Section */}
      {stage !== 'idle' && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className={`text-sm font-medium ${getStageColor()}`}>
              {message}
            </span>
            <span className="text-sm text-gray-400">
              {progress}%
            </span>
          </div>
          
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Detailed progress for current stage */}
          {stage === 'generating_frames' && (
            <div className="text-xs text-gray-500">
              Frame {frameProgress.currentFrame} of {frameProgress.totalFrames}
            </div>
          )}
          
          {stage === 'uploading_frames' && (
            <div className="text-xs text-gray-500">
              Uploaded {uploadProgress.uploadedFrames} of {uploadProgress.totalFrames} frames
            </div>
          )}
          
          {stage === 'processing_video' && processingProgress.processingTime && (
            <div className="text-xs text-gray-500">
              Processing time: {Math.round(processingProgress.processingTime)}s
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-3">
        <button
          onClick={generateVideo}
          disabled={isGenerating}
          className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          {isGenerating ? 'Generating...' : 'Generate Video (Server)'}
        </button>

        {stage === 'error' && (
          <button
            onClick={handleRetry}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            Retry
          </button>
        )}

        {sessionId && stage !== 'generating_frames' && stage !== 'uploading_frames' && stage !== 'processing_video' && (
          <button
            onClick={handleCleanup}
            disabled={isGenerating}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
          >
            Cleanup
          </button>
        )}

        {/* Test buttons for development */}
        {process.env.NODE_ENV === 'development' && (
          <>
            <button
              onClick={handleRunTests}
              disabled={isGenerating}
              className="px-3 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors text-sm"
            >
              Test
            </button>
            <button
              onClick={handleFullDebug}
              disabled={isGenerating}
              className="px-3 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors text-sm"
            >
              Debug
            </button>
          </>
        )}
      </div>

      {/* Result */}
      {videoUrl && stage === 'complete' && (
        <div className="mt-4 p-4 bg-green-900/20 border border-green-700 rounded-lg">
          <h4 className="text-green-400 font-medium mb-2">Video Ready!</h4>
          <div className="space-y-2">
            <video 
              controls 
              className="w-full max-w-md rounded-lg"
              src={videoUrl}
            >
              Your browser does not support the video tag.
            </video>
            <a
              href={videoUrl}
              download="generated-video.mp4"
              className="inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Download Video
            </a>
          </div>
        </div>
      )}

      {/* Error Display */}
      {stage === 'error' && (
        <div className="mt-4 p-4 bg-red-900/20 border border-red-700 rounded-lg">
          <h4 className="text-red-400 font-medium mb-2">Generation Failed</h4>
          <p className="text-red-300 text-sm">{message}</p>
        </div>
      )}

      {/* Info */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• Server-side processing with no browser memory limits</p>
        <p>• Frames compressed as WebP and temporarily stored</p>
        <p>• High-quality video generation using rendi.dev</p>
        <p>• Automatic cleanup of temporary files</p>
      </div>
    </div>
  );
}
