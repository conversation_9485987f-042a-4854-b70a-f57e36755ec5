import {
  RendiCommandRequest,
  RendiCommandResponse,
  RendiCommandStatus
} from '../../types';
import { RendiProcessingProgress } from './types';

/**
 * Rendi.dev API client for video generation (uses Next.js API route)
 */
export class RendiApiClient {
  private apiRoute: string = '/api/rendi';

  constructor() {
    // No API key needed on client side - handled by server route
  }

  /**
   * Submit an FFmpeg command to rendi.dev via API route
   */
  async submitCommand(request: RendiCommandRequest): Promise<RendiCommandResponse> {
    try {
      const response = await fetch(this.apiRoute, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'submit',
          ...request
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API error (${response.status})`);
      }

      return await response.json();
    } catch (error) {
      throw new Error(`Failed to submit command to Rendi: ${error}`);
    }
  }

  /**
   * Poll the status of a command via API route
   */
  async pollCommand(commandId: string): Promise<RendiCommandStatus> {
    try {
      const response = await fetch(this.apiRoute, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'poll',
          commandId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API error (${response.status})`);
      }

      return await response.json();
    } catch (error) {
      throw new Error(`Failed to poll command status: ${error}`);
    }
  }

  /**
   * Wait for command completion with progress updates
   */
  async waitForCompletion(
    commandId: string,
    onProgress?: (progress: RendiProcessingProgress) => void,
    pollInterval: number = 2000,
    maxWaitTime: number = 300000 // 5 minutes
  ): Promise<RendiCommandStatus> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      try {
        const status = await this.pollCommand(commandId);
        
        // Calculate progress based on status
        let percentage = 0;
        let message = 'Processing video...';
        
        switch (status.status) {
          case 'PENDING':
            percentage = 10;
            message = 'Command queued for processing...';
            break;
          case 'RUNNING':
            percentage = 50;
            message = 'Generating video on server...';
            break;
          case 'SUCCESS':
            percentage = 100;
            message = 'Video generation completed!';
            break;
          case 'FAILED':
            percentage = 0;
            message = `Generation failed: ${status.error_message || 'Unknown error'}`;
            break;
        }
        
        if (onProgress) {
          onProgress({
            status: status.status.toLowerCase() as any,
            percentage,
            message,
            processingTime: status.total_processing_seconds
          });
        }
        
        // Return if completed (success or failure)
        if (status.status === 'SUCCESS' || status.status === 'FAILED') {
          return status;
        }
        
        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        
      } catch (error) {
        if (onProgress) {
          onProgress({
            status: 'failed',
            percentage: 0,
            message: `Polling error: ${error}`
          });
        }
        throw error;
      }
    }
    
    throw new Error(`Command ${commandId} timed out after ${maxWaitTime}ms`);
  }

  /**
   * Create FFmpeg command for generating video from image sequence
   * Uses a simpler approach that works well with rendi.dev
   */
  createVideoFromImagesCommand(
    frameUrls: string[],
    audioUrl: string | null,
    outputFileName: string,
    fps: number = 25,
    resolution: string = '1280x720'
  ): RendiCommandRequest {
    // For rendi.dev, we'll use a different approach
    // We'll create a simple slideshow-style video where each frame is shown for 1/fps seconds

    const inputFiles: Record<string, string> = {};

    // Add only the first frame for now (we'll enhance this later)
    inputFiles['in_frame'] = frameUrls[0];

    // Add audio if provided
    if (audioUrl) {
      inputFiles['in_audio'] = audioUrl;
    }

    // Calculate video duration based on number of frames and fps
    const duration = frameUrls.length / fps;

    // Simple command to create video from single image with audio
    let command = `-loop 1 -i {{in_frame}}`;

    if (audioUrl) {
      command += ` -i {{in_audio}}`;
      command += ` -c:v libx264 -preset medium -crf 23`;
      command += ` -c:a aac -b:a 128k`;
      command += ` -pix_fmt yuv420p`;
      command += ` -shortest`; // End when audio ends
    } else {
      command += ` -c:v libx264 -preset medium -crf 23`;
      command += ` -pix_fmt yuv420p`;
      command += ` -t ${duration}`; // Set duration
    }

    command += ` -vf "scale=${resolution}:force_original_aspect_ratio=decrease,pad=${resolution}:-1:-1"`;
    command += ` {{out_video}}`;

    return {
      ffmpeg_command: command,
      input_files: inputFiles,
      output_files: {
        out_video: outputFileName
      },
      max_command_run_seconds: 300, // 5 minutes max
      vcpu_count: 2 // Use 2 vCPUs for simpler processing
    };
  }

  /**
   * Simplified method to generate video from frame URLs
   */
  async generateVideoFromFrames(
    frameUrls: string[],
    audioUrl: string | null,
    fps: number = 25,
    resolution: string = '1280x720',
    onProgress?: (progress: RendiProcessingProgress) => void
  ): Promise<string> {
    const outputFileName = `video_${Date.now()}.mp4`;
    
    // Create and submit command
    const command = this.createVideoFromImagesCommand(
      frameUrls,
      audioUrl,
      outputFileName,
      fps,
      resolution
    );
    
    if (onProgress) {
      onProgress({
        status: 'pending',
        percentage: 5,
        message: 'Submitting video generation request...'
      });
    }
    
    const response = await this.submitCommand(command);
    
    if (onProgress) {
      onProgress({
        status: 'running',
        percentage: 10,
        message: 'Request submitted, waiting for processing...'
      });
    }
    
    // Wait for completion
    const result = await this.waitForCompletion(response.command_id, onProgress);
    
    if (result.status !== 'SUCCESS') {
      throw new Error(`Video generation failed: ${result.error_message || 'Unknown error'}`);
    }
    
    // Get the output video URL
    const outputFile = result.output_files?.out_video;
    if (!outputFile?.storage_url) {
      throw new Error('No output video URL found in response');
    }
    
    return outputFile.storage_url;
  }
}
