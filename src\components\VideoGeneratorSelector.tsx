'use client';

import React, { useState } from 'react';
import { Monitor, Server, Info } from 'lucide-react';
import { VideoGeneratorProps, VideoGenerationMethod } from '@/types';
import VideoGenerator from './VideoGenerator';
import RendiVideoGenerator from '../video-generation/rendi/RendiVideoGenerator';

export default function VideoGeneratorSelector(props: VideoGeneratorProps) {
  const [selectedMethod, setSelectedMethod] = useState<VideoGenerationMethod>('browser');

  const methods = [
    {
      id: 'browser' as VideoGenerationMethod,
      name: 'Browser-based (FFmpeg)',
      icon: Monitor,
      description: 'Generate video in your browser using WebAssembly',
      pros: [
        'No server dependency',
        'Immediate processing',
        'Full privacy (no uploads)'
      ],
      cons: [
        'Memory limitations',
        'May crash on long videos',
        'Slower processing'
      ],
      recommended: 'For short videos (< 3 minutes)'
    },
    {
      id: 'rendi' as VideoGenerationMethod,
      name: 'Server-side (Rendi.dev)',
      icon: Server,
      description: 'Generate video on powerful servers with no memory limits',
      pros: [
        'No memory limitations',
        'Faster processing',
        'High-quality output',
        'Handles long videos'
      ],
      cons: [
        'Requires internet connection',
        'Temporary file uploads',
        'Server processing time'
      ],
      recommended: 'For longer videos or better performance'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Method Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
          <Info className="w-5 h-5" />
          Choose Generation Method
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {methods.map((method) => {
            const Icon = method.icon;
            const isSelected = selectedMethod === method.id;
            
            return (
              <div
                key={method.id}
                className={`
                  relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200
                  ${isSelected 
                    ? 'border-purple-500 bg-purple-900/20' 
                    : 'border-gray-700 bg-gray-800/50 hover:border-gray-600'
                  }
                `}
                onClick={() => setSelectedMethod(method.id)}
              >
                {/* Selection indicator */}
                {isSelected && (
                  <div className="absolute top-3 right-3 w-3 h-3 bg-purple-500 rounded-full" />
                )}
                
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex items-center gap-3">
                    <Icon className={`w-6 h-6 ${isSelected ? 'text-purple-400' : 'text-gray-400'}`} />
                    <div>
                      <h4 className={`font-semibold ${isSelected ? 'text-white' : 'text-gray-300'}`}>
                        {method.name}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {method.description}
                      </p>
                    </div>
                  </div>
                  
                  {/* Pros and Cons */}
                  <div className="space-y-2 text-xs">
                    <div>
                      <span className="text-green-400 font-medium">Pros:</span>
                      <ul className="text-gray-400 ml-2">
                        {method.pros.map((pro, index) => (
                          <li key={index}>• {pro}</li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <span className="text-yellow-400 font-medium">Cons:</span>
                      <ul className="text-gray-400 ml-2">
                        {method.cons.map((con, index) => (
                          <li key={index}>• {con}</li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="pt-1 border-t border-gray-700">
                      <span className="text-blue-400 font-medium">Best for:</span>
                      <span className="text-gray-400 ml-2">{method.recommended}</span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Selected Method Component */}
      <div className="bg-black/20 backdrop-blur-sm rounded-xl p-6 border border-gray-800">
        {selectedMethod === 'browser' ? (
          <VideoGenerator {...props} />
        ) : (
          <RendiVideoGenerator {...props} />
        )}
      </div>

      {/* Additional Info */}
      <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="space-y-2 text-sm">
            <h4 className="text-blue-400 font-medium">Generation Method Comparison</h4>
            <div className="text-gray-300 space-y-1">
              <p>
                <strong>Browser-based:</strong> Processes everything locally in your browser. 
                Good for privacy and short videos, but limited by your device's memory.
              </p>
              <p>
                <strong>Server-side:</strong> Uploads compressed frames to temporary storage 
                and processes on powerful servers. Better for longer videos and consistent quality.
              </p>
            </div>
            <div className="text-xs text-gray-400 pt-2 border-t border-blue-800">
              <p>
                Both methods produce the same visual quality. Choose based on your video length 
                and performance preferences.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
