# Rendi.dev Video Generation

This module implements server-side video generation using the rendi.dev API as an alternative to browser-based FFmpeg processing.

## Overview

The rendi.dev video generation method provides:
- **Server-side processing**: No browser memory limitations
- **High-quality output**: Consistent video quality using powerful servers
- **Better performance**: Faster processing for longer videos
- **Automatic cleanup**: Temporary files are automatically cleaned up

## Architecture

### Components

1. **RendiVideoGenerator.tsx**: Main React component that orchestrates the entire process
2. **frame-generator.ts**: Generates visualization frames using P5.js
3. **supabase-utils.ts**: Handles frame storage and compression using Supabase
4. **api-client.ts**: Communicates with rendi.dev API via Next.js API route
5. **test-utils.ts**: Testing utilities for validation

### Process Flow

1. **Frame Generation**: Generate visualization frames using P5.js (same as browser method)
2. **Compression**: Convert frames to WebP format for better compression
3. **Upload**: Upload compressed frames to Supabase storage in chunks
4. **Processing**: Submit video generation request to rendi.dev
5. **Polling**: Monitor processing status and provide progress updates
6. **Cleanup**: Remove temporary files after completion

## Configuration

### Environment Variables

Required in `.env.local`:

```env
# Rendi.dev API Key
RENDI_API_KEY=your_rendi_api_key_here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_SUPABASE_BUCKET=temp-video-files
```

### Supabase Setup

1. Create a bucket named `temp-video-files`
2. Set bucket to public access
3. Configure appropriate file size limits (recommended: 100MB)
4. Set up automatic cleanup policies if desired

## Usage

### Basic Usage

```tsx
import RendiVideoGenerator from './video-generation/rendi/RendiVideoGenerator';

<RendiVideoGenerator
  audioData={audioData}
  style={selectedStyle}
  settings={videoSettings}
  visualizationParams={visualizationParams}
  onGenerationStart={() => setIsGenerating(true)}
  onGenerationComplete={() => setIsGenerating(false)}
/>
```

### Testing

The component includes built-in testing utilities:

```typescript
import { runAllTests, logTestResults } from './test-utils';

// Run all integration tests
const results = await runAllTests();
logTestResults(results);
```

## API Routes

### `/api/rendi`

Handles communication with rendi.dev API:

- **POST** with `action: 'submit'`: Submit FFmpeg command
- **POST** with `action: 'poll'`: Poll command status

## Error Handling

The implementation includes comprehensive error handling:

- **Storage errors**: Supabase connectivity and upload failures
- **API errors**: Rendi.dev API communication issues
- **Processing errors**: Video generation failures
- **Timeout handling**: Automatic timeout for long-running processes
- **Cleanup on error**: Automatic cleanup of temporary files

## Limitations

1. **Frame sequence limitation**: Currently uses a simplified approach (single frame loop) due to rendi.dev input file limitations
2. **Audio support**: Audio integration is prepared but not yet implemented
3. **Processing time**: Server processing may take 2-3 minutes for longer videos
4. **File size**: Limited by Supabase storage quotas

## Future Enhancements

1. **True frame sequence**: Implement proper frame-by-frame video generation
2. **Audio integration**: Add audio track to generated videos
3. **Progress estimation**: Better progress estimation based on video length
4. **Batch optimization**: Optimize frame upload batching for better performance
5. **Caching**: Cache frequently used frames or patterns

## Troubleshooting

### Common Issues

1. **"Storage service is not available"**
   - Check Supabase configuration
   - Verify bucket exists and is accessible
   - Check network connectivity

2. **"Rendi API connectivity failed"**
   - Verify RENDI_API_KEY is set correctly
   - Check rendi.dev service status
   - Verify API route is working

3. **"Frame upload incomplete"**
   - Check internet connection stability
   - Verify Supabase storage limits
   - Try reducing video length or quality

### Debug Mode

In development mode, use the "Test" button to run integration tests and verify all components are working correctly.

## Performance Considerations

- **Frame generation**: Processed in batches to manage memory usage
- **Upload optimization**: Frames uploaded in chunks to prevent timeouts
- **Compression**: WebP format reduces upload time and storage usage
- **Cleanup**: Automatic cleanup prevents storage bloat

## Security

- API key is handled server-side only
- Temporary files are automatically cleaned up
- Public URLs are time-limited through Supabase configuration
- No sensitive data is exposed to the client
