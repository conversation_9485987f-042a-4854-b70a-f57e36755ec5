// Test utilities for rendi.dev video generation

import { checkStorageHealth, generateSessionId } from './supabase-utils';
import { RendiApiClient } from './api-client';

/**
 * Test Supabase storage connectivity with detailed diagnostics
 */
export async function testStorageConnectivity(): Promise<{ success: boolean; message: string }> {
  try {
    // Test 1: Check if Supabase client can be created
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const bucketName = process.env.NEXT_PUBLIC_SUPABASE_BUCKET;

    if (!supabaseUrl || !supabaseKey || !bucketName) {
      return {
        success: false,
        message: 'Missing Supabase environment variables (URL, KEY, or BUCKET)'
      };
    }

    // Test 2: Check bucket accessibility
    const isHealthy = await checkStorageHealth();
    if (!isHealthy) {
      return {
        success: false,
        message: `Bucket '${bucketName}' is not accessible. Check bucket exists and has public policies.`
      };
    }

    // Test 3: Try a simple upload test (create a tiny test file)
    try {
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(supabaseUrl, supabaseKey);

      const testFileName = `test_${Date.now()}.txt`;
      const testContent = new Blob(['test'], { type: 'text/plain' });

      const { error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(testFileName, testContent);

      if (uploadError) {
        return {
          success: false,
          message: `Upload test failed: ${uploadError.message}. Check bucket policies.`
        };
      }

      // Clean up test file
      await supabase.storage.from(bucketName).remove([testFileName]);

      return {
        success: true,
        message: 'Storage is fully accessible (bucket found, policies OK, upload/delete working)'
      };

    } catch (uploadTestError) {
      return {
        success: false,
        message: `Upload test error: ${uploadTestError instanceof Error ? uploadTestError.message : 'Unknown error'}`
      };
    }

  } catch (error) {
    return {
      success: false,
      message: `Storage test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Test rendi.dev API connectivity
 */
export async function testRendiApiConnectivity(): Promise<{ success: boolean; message: string }> {
  try {
    const client = new RendiApiClient();
    
    // Test with a simple command that should fail gracefully
    const testCommand = {
      ffmpeg_command: '-f lavfi -i testsrc=duration=1:size=320x240:rate=1 -t 1 {{out_test}}',
      input_files: {},
      output_files: {
        out_test: 'test.mp4'
      },
      max_command_run_seconds: 30,
      vcpu_count: 1
    };
    
    // This should at least reach the API (even if it fails due to invalid command)
    await client.submitCommand(testCommand);
    
    return {
      success: true,
      message: 'Rendi API is accessible'
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    // Check if it's a connectivity issue vs API issue
    if (errorMessage.includes('fetch') || errorMessage.includes('network') || errorMessage.includes('ENOTFOUND')) {
      return {
        success: false,
        message: `Rendi API connectivity failed: ${errorMessage}`
      };
    } else {
      // API responded but with an error (which is expected for our test)
      return {
        success: true,
        message: 'Rendi API is accessible (test command rejected as expected)'
      };
    }
  }
}

/**
 * Test session ID generation
 */
export function testSessionIdGeneration(): { success: boolean; message: string } {
  try {
    const sessionId1 = generateSessionId();
    const sessionId2 = generateSessionId();
    
    if (!sessionId1 || !sessionId2) {
      return {
        success: false,
        message: 'Session ID generation returned empty values'
      };
    }
    
    if (sessionId1 === sessionId2) {
      return {
        success: false,
        message: 'Session IDs are not unique'
      };
    }
    
    if (!sessionId1.startsWith('session_')) {
      return {
        success: false,
        message: 'Session ID format is incorrect'
      };
    }
    
    return {
      success: true,
      message: `Session ID generation working (example: ${sessionId1})`
    };
  } catch (error) {
    return {
      success: false,
      message: `Session ID generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Run all tests
 */
export async function runAllTests(): Promise<{
  storage: { success: boolean; message: string };
  api: { success: boolean; message: string };
  sessionId: { success: boolean; message: string };
  overall: { success: boolean; message: string };
}> {
  console.log('Running rendi.dev integration tests...');
  
  const storage = await testStorageConnectivity();
  const api = await testRendiApiConnectivity();
  const sessionId = testSessionIdGeneration();
  
  const allPassed = storage.success && api.success && sessionId.success;
  
  const overall = {
    success: allPassed,
    message: allPassed 
      ? 'All tests passed - rendi.dev integration is ready'
      : 'Some tests failed - check individual test results'
  };
  
  return { storage, api, sessionId, overall };
}

/**
 * Log test results to console
 */
export function logTestResults(results: Awaited<ReturnType<typeof runAllTests>>): void {
  console.log('=== Rendi.dev Integration Test Results ===');
  console.log(`Storage: ${results.storage.success ? '✅' : '❌'} ${results.storage.message}`);
  console.log(`API: ${results.api.success ? '✅' : '❌'} ${results.api.message}`);
  console.log(`Session ID: ${results.sessionId.success ? '✅' : '❌'} ${results.sessionId.message}`);
  console.log(`Overall: ${results.overall.success ? '✅' : '❌'} ${results.overall.message}`);
  console.log('==========================================');
}
