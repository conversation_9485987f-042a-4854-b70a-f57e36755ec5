import { NextRequest, NextResponse } from 'next/server';

const RENDI_API_BASE = 'https://api.rendi.dev/v1';
const API_KEY = process.env.RENDI_API_KEY;

if (!API_KEY) {
  console.error('RENDI_API_KEY environment variable is not set');
}

export async function POST(request: NextRequest) {
  if (!API_KEY) {
    return NextResponse.json(
      { error: 'Rendi API key not configured' },
      { status: 500 }
    );
  }

  try {
    const body = await request.json();
    const { action, ...data } = body;

    let url: string;
    let method: string = 'POST';

    switch (action) {
      case 'submit':
        url = `${RENDI_API_BASE}/run-ffmpeg-command`;
        method = 'POST';
        break;
      case 'poll':
        url = `${RENDI_API_BASE}/commands/${data.commandId}`;
        method = 'GET';
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': API_KEY,
      },
      body: method === 'POST' ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        { error: `Rendi API error (${response.status}): ${errorText}` },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json(result);

  } catch (error) {
    console.error('Rendi API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
